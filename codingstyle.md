# Python Coding Style Guidelines

## Naming 
Single character variable names
Do not use lower case “l”, uppercase “O” or uppercase “I” these can be easily confused with number in some fonts. You can use underscore as a single character variable name.

### Names to avoid
•	Avoid using reserved words for user-defined names.
Packages and module names
•	Use lower case and short names if possible. Underscores can be used if needed for clarity, but they are discouraged.

| Symbol      | Naming Convention | Example         |
|-------------|-------------------|-----------------|
| Variable    | camelCase         | computerName    |
| Method      | camelCase         | readLine()      |
| Parameter   | camelCase         | userInput       |
| Acronyms    | CapitalCase       | HTTPServerError |


## Program layout
### Indentation
Python uses indentation to denote a block of code. Use 4 spaces instead of tabs. 

### Blank lines
Use:
•	Blank lines to separate groups of related functions. 
•	Blank lines between multiple related one-liners; they can be omitted too.
•	Blank lines in functions to denote logical sections.
 
## Comments
Comments help in making the code more readable to programmers, especially when multiple programmers need to collaborate on maintaining the code. Good practice with comments include:
•	Update comments as soon as the code is updated.
•	Write comments in full sentences, including capitalisation and punctuation.
•	Block comments may include multiple sentences.
•	Comments must be clear and unambiguous.
•	Use block comments and avoid inline comments.

### Block comments
Use block comments to clearly describe the block's purpose and operation details. Comments are preceded by the “#” symbol and a single space. Multiline comments follow the same style.

### Inline comments
The use of inline comments is discouraged in Python for readability purposes. However, if the programmer feels that inline comments will add value to the code, they can be included. Inline comments follow the same style as block comments.

## String quotes
Single and double-string quotes have the same meaning and functionality in Python. For consistency, select and use one single style throughout the code.
White spaces in statements
•	Use a white space after a comma. 
•	Add a space around operators: a + b - c = d. 