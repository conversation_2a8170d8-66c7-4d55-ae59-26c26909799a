[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "computer-fingerprint-reader"
version = "1.0.0"
description = "A Python program to collect computer system information and maintain a CSV database for Midtown IT network"
authors = [
    {name = "Midtown IT", email = "<EMAIL>"}
]
requires-python = ">=3.7"
dependencies = [
    "psutil>=5.8.0",
    "speedtest-cli>=2.1.3",
]

[project.scripts]
fingy = "main:main"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.hatch.build.targets.sdist]
include = [
    "/main.py",
]

[tool.black]
line-length = 88
target-version = ['py37']

[tool.isort]
profile = "black"
line_length = 88
