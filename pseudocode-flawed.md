Function main
Input: None  
Output: None

Try the following:  
    Print starting message
    fingerprint = call fingerprint function
    check fingerprint against existing data
    Save fingerprint to file  
Catch keyboard interrupt  
    Print operation cancelled by user  
    Exit program  

End


Function fingerprint  
Input: None  
Output: fingerprint dictionary

Get computer name  
Get IP address  
Get MAC address  
Get processor model  
Get operating system  
Get system time  
Get internet speed  
Get active ports  

Store all values in a fingerprint dictionary  
Return fingerprint  

End function

Function get computer name  
Input: None  
Output: computer name or "Unknown"

Try to get system hostname  
If unsuccessful, return "Unknown"  
Return name  

End function

Function get IP address  
Input: None  
Output: IP address or "Unknown"

Try to resolve IP from system hostname  
If unsuccessful, return "Unknown"  
Return IP address  

End function

Function get MAC address  
Input: None  
Output: MAC address or "Unknown"

Try to list all network interfaces  
For each interface  
    For each address in interface  
        If address type is MAC  
            Return MAC address  
If no MAC address found  
    Return "Unknown"  

End function

Function get processor model  
Input: None  
Output: processor model or "Unknown"

Try to read system processor information  
If unavailable, return "Unknown"  
Return processor model  

End function

Function get operating system  
Input: None  
Output: operating system string or "Unknown"

Try to get system name and version  
Return formatted system name and version  
If unavailable, return "Unknown"  

End function

Function get system time  
Input: None  
Output: formatted date and time string or "Unknown"

Try to read current system time  
Return time formatted as day month year hour:minute:second  
If unavailable, return "Unknown"  

End function

Function get internet speed  
Input: None  
Output: speed in Mbps as string or message

Try to perform a download speed test  
If speed test tool is not available  
    return "Speed test unavailable"  
If test fails  
    return "Test failed"  
Return speed as string  

End function

Function get active ports  
Input: None  
Output: list of all active ports as semicolon-separated string or "Unknown"

Try to list active network connections  
For each connection  
    If connection is listening and has a valid port  
        Add port to list if not already added  

Sort the list  
Join ports into semicolon-separated string and return  
If process fails, return "Unknown"  

End function

Function collect fingerprint  
Input: None  
Output: fingerprint dictionary

Create empty fingerprint dictionary
Add to fingerprint dictionary: 
    Set Computer Name = get computer name
    Set IP Address = get IP address
    Set MAC Address = get MAC address
    Set Processor Model = get processor model
    Set Operating System = get operating system
    Set System Time from = system time

    Call concurrently:
        Set Internet Speed = get internet speed
        Set Active Ports = get active ports

Return fingerprint  
End


Function compare to existing data  
Input: CSV file, fingerprint data  
Output: True if identical data exists, False otherwise

If CSV file does not exist  
    Return False  

Format fingerprint data as CSV-formatted string  
Read contents of CSV file as raw string  

If fingerprint string is found in file  
    Return True  
Return False  

End function

Function save CSV file  
Input: fingerprint  
Output: None

Try the following:
    Create new CSV file
    Convert fingerprint object to CSV
    Write CSV to file
    Print success message

Catch any exception
    Print error message

End function