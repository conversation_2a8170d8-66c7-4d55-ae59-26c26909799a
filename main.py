import csv
import datetime
import platform
import socket
from typing import Dict

import psutil


def get_computer_name() -> str:
    """
    Get the computer name.

    Returns:
        Computer name as string or "Unknown"
    """
    try:
        return platform.node()
    except Exception:
        return "Unknown"


def get_ip_address() -> str:
    """
    Get the primary IP address of the computer.

    Returns:
        IP address as string or "Unknown"
    """
    try:
        hostName = socket.gethostname()
        ipAddress = socket.gethostbyname(hostName)
        return ipAddress
    except Exception:
        return "Unknown"


def get_mac_address() -> str:
    """
    Get the MAC address of the primary network interface.

    Returns:
        MAC address as string or "Unknown"
    """
    try:
        interfaces = psutil.net_if_addrs()

        for _, interfaceAddresses in interfaces.items():
            for address in interfaceAddresses:
                if address.family == psutil.AF_LINK:
                    return address.address
        return "Unknown"
    except Exception:
        return "Unknown"


def get_processor_model() -> str:
    """
    Get the processor model information.

    Returns:
        Processor model as string or "Unknown"
    """
    try:
        return platform.processor()
    except Exception:
        return "Unknown"


def get_operating_system() -> str:
    """
    Get the operating system information.

    Returns:
        Operating system details as string or "Unknown"
    """
    try:
        system = platform.system()
        release = platform.release()
        return f"{system} {release}"
    except Exception:
        return "Unknown"


def get_system_time() -> str:
    """
    Get the current system time in required format.

    Returns:
        System time as formatted string or "Unknown"
    """
    try:
        now = datetime.datetime.now()
        return now.strftime("%d//%m//%Y %H:%M:%S")
    except Exception:
        return "Unknown"


def get_internet_speed() -> str:
    """
    Test internet connection speed.

    Returns:
        Internet speed in Mb/s as string or message
    """
    try:
        import speedtest

        st = speedtest.Speedtest()
        st.get_best_server()
        downloadSpeed = st.download()
        speedMbps = downloadSpeed / 1_000_000
        return f"{speedMbps:.0f}Mb/s"
    except ImportError:
        return "Speed test unavailable"
    except Exception:
        return "Test failed"


def get_active_ports() -> str:
    """
    Get list of active network ports.

    Returns:
        Semicolon-delimited list of active ports as string or "Unknown"
    """
    try:
        activePorts = []
        connections = psutil.net_connections()

        for connection in connections:
            if connection.laddr and connection.status == 'LISTEN':
                port = connection.laddr.port
                if port not in activePorts:
                    activePorts.append(port)

        activePorts.sort()
        return ";".join(map(str, activePorts))
    except Exception:
        return "Unknown"


def fingerprint() -> Dict[str, str]:
    """
    Collect all system information to create a computer fingerprint.

    Returns:
        Dictionary containing all system information
    """
    fingerprintData = {
        'Computer Name': get_computer_name(),
        'IP Address': get_ip_address(),
        'MAC Address': get_mac_address(),
        'Processor Model': get_processor_model(),
        'Operating System': get_operating_system(),
        'System Time': get_system_time(),
        'Internet Speed': get_internet_speed(),
        'Active Ports': get_active_ports()
    }

    return fingerprintData


def compare_to_existing_data(csvFileName: str, fingerprintData: Dict[str, str]) -> bool:
    """
    Compare fingerprint to existing data in CSV file.

    Args:
        csvFileName: Name of the CSV file
        fingerprintData: Current computer fingerprint

    Returns:
        True if identical data exists, False otherwise
    """
    try:
        # If CSV file does not exist, return False
        with open(csvFileName, 'r', newline='', encoding='utf-8') as file:
            fileContents = file.read()
    except FileNotFoundError:
        return False

    # Format fingerprint data as CSV-formatted string
    fingerprintValues = list(fingerprintData.values())
    fingerprintString = ','.join(f'"{value}"' for value in fingerprintValues)

    # If fingerprint string is found in file, return True
    if fingerprintString in fileContents:
        return True

    return False


def save_csv_file(fingerprintData: Dict[str, str], csvFileName: str) -> None:
    """
    Save fingerprint to CSV file.

    Args:
        fingerprintData: Dictionary containing system information
        csvFileName: Name of the CSV file to store computer fingerprints
    """
    fieldNames = [
        'Computer Name',
        'IP Address',
        'MAC Address',
        'Processor Model',
        'Operating System',
        'System Time',
        'Internet Speed',
        'Active Ports'
    ]

    try:
        # Check if file exists and has data
        fileExists = False
        try:
            with open(csvFileName, 'r', newline='', encoding='utf-8') as file:
                content = file.read().strip()
                if content:
                    fileExists = True
        except FileNotFoundError:
            pass

        # Open file in append mode if it exists, write mode if it doesn't
        mode = 'a' if fileExists else 'w'

        with open(csvFileName, mode, newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=fieldNames)

            # Write header only if file is new
            if not fileExists:
                writer.writeheader()

            writer.writerow(fingerprintData)

        print(f"Computer fingerprint saved to {csvFileName}")

    except Exception as e:
        print(f"Error saving to CSV: {e}")


def main():
    """
    Main function to run fingerprinting.
    """
    try:
        # Print starting message
        print("Collecting computer fingerprint information...")

        # Call fingerprint function
        fingerprintData = fingerprint()

        # Check fingerprint against existing data
        csvFileName = "computer_info.csv"
        if not compare_to_existing_data(csvFileName, fingerprintData):
            # Save fingerprint to file
            save_csv_file(fingerprintData, csvFileName)
        else:
            print("Identical fingerprint already exists in the file.")

        print("\nCompleted successfully!")

    except KeyboardInterrupt:
        # Print operation cancelled by user
        print("\nOperation cancelled by user")
        # Exit program
        return


if __name__ == "__main__":
    main()

