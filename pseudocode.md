Function main
Input: None
Output: None

Try
    Print starting message
    fingerprintData = call collectFingerprint function
    csvFilename = "computer_info.csv"
    existingData = loadExistingCSVData

    Call processComputerFingerprint with fingerprintData, existingData
    Save existingData to csvFilename
    Print completion message
Catch keyboard interrupt
    Print operation cancelled by user
    Exit program

End function


Function processComputerFingerprint
Input: fingerprintData, existingData
Output: None

existingRecord = call findExistingRecord function
If existingRecord found:
    Call handleExistingComputer
Else:
    Call handleNewComputer

End function


Function handleExistingComputer
Input: fingerprintData, existingRecord, existingData
Output: None

differences = compareFingerprintWithExistingRecord
If differences found:
    shouldContinue = call displayWarningsAndPrompt with differences
    If not shouldContinue:
        Print "Operation cancelled"
        Exit program

Call updateExistingRecord with existingData, existingRecord, fingerprintData

End function


Function handleNewComputer
Input: fingerprintData, existingData
Output: None

Add fingerprintData to existingData

End function


Function displayWarningsAndPrompt
Input: differences list
Output: True if user wants to continue, False otherwise

Display warning messages for each changed field in differences
Prompt user "Continue? (Y/n): "
userInput = read user input
If userInput is "n" or "N":
    Return False
Return True

End function


Function collectFingerprint
Input: None
Output: fingerprint dictionary

Get computer name
Get IP address
Get MAC address
Get processor model
Get operating system
Get system time
Get internet speed
Get active ports

Store all values in a fingerprint dictionary
Return fingerprint

End function

Function getComputerName
Input: None
Output: computer name or "Unknown"

Try to getSystemHostname
If unsuccessful, return "Unknown"
Return name

End function

Function getIPAddress
Input: None
Output: IP address or "Unknown"

Try to resolve IP from system hostname
If unsuccessful, return "Unknown"
Return IP address

End function

Function getMACAddress
Input: None
Output: MAC address or "Unknown"

Try to list all network interfaces
For each interface
    For each address in interface
        If address type is MAC
            Return MAC address
If no MAC address found
    Return "Unknown"

End function

Function getProcessorModel
Input: None
Output: processor model or "Unknown"

Try to read system processor information
If unavailable, return "Unknown"
Return processor model

End function

Function getOperatingSystem
Input: None
Output: operating system string or "Unknown"

Try to get system name and version
Return formatted system name and version
If unavailable, return "Unknown"

End function

Function getSystemTime
Input: None
Output: formatted date and time string or "Unknown"

Try to read current system time
Return time formatted as day month year hour:minute:second
If unavailable, return "Unknown"

End function

Function getInternetSpeed
Input: None
Output: speed in Mbps as string or message

Try to perform a download speed test
If speed test tool is not available
    return "Speed test unavailable"
If test fails
    return "Test failed"
Return speed as string

End function

Function getActivePorts
Input: None
Output: list of all active ports as semicolon-separated string or "Unknown"

Try to list active network connections
For each connection
    If connection is listening and has a valid port
        Add port to list if not already added

Sort the list
Join ports into semicolon-separated string and return
If process fails, return "Unknown"

End function

Function loadExistingCSVData
Input: CSV filename
Output: List of existing computer records

If CSV file does not exist
    Return empty list

Try to read CSV file
    Open CSV file
    Read all records into list of dictionaries
    Return list of records
Catch any exception
    Return empty list

End function

Function findExistingRecord
Input: existing data list, computer name, MAC address
Output: existing record or None

For each record in existing data
    If record computer name matches current computer name OR
       record MAC address matches current MAC address
        Return record
Return None

End function

Function compareFingerprintWithExistingRecord
Input: current fingerprint, existing record
Output: list of differences

Create empty differences list
Compare each field except "System Time":
    If current fingerprint field differs from existing record field
        Add difference description to differences list
Return differences list

End function

Function saveCSVFile
Input: data list, CSV filename
Output: None

Try
    Open CSV file for writing
    Write header row
    Write all data records to CSV
    Print success message

Catch any exception
    Print error message

End function


Function updateExistingRecord
Input: existingData, existingRecord, newFingerprintData
Output: None

Find index of existingRecord in existingData
Replace record at that index with newFingerprintData

End function